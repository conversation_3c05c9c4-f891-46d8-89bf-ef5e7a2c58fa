from datetime import datetime
from fastapi import APIRouter, Request
from app.service.fengniao import FengniaoClient

router = APIRouter()
fengniao_client = FengniaoClient()
options = {
    "preCreateOrder": "预下单",
    "createOrder": "正式创建订单",
    "getOrderDetail": "查询订单",
    "getCancelReasonList": "查询订单取消原因",
    "preCancelOrder": "预取消订单",
    "cancelOrder": "正式取消订单"
}

@router.post("/invoke_fengniao_api/{api_name}", summary="蜂鸟api接口调用")
async def invoke_fengniao_api(api_name: str, data: Request):
    try:
        if not options.get(api_name):
            return {"code": 400, "status": "error", "message": f"无效的api_name: {api_name}"}
        
        body = await data.json()
        print (f"[{datetime.now()}] 调用蜂鸟api: {api_name}, 请求参数: {body}")

        resp = fengniao_client.invoke(api_name, body)
        print (f"[{datetime.now()}] 调用蜂鸟api: {api_name}, 响应参数: {resp}")
        
        return resp
    except Exception as e:
        return {"code": 500, "status": "error", "message": f"服务器错误: {str(e)}"}
