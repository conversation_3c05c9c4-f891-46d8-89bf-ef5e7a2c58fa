"""add_fengniao_callback_tables

Revision ID: add_fengniao_callback_tables
Revises: dc3c3d25c139
Create Date: 2025-01-24 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.mysql import JSON, BIGINT


# revision identifiers, used by Alembic.
revision: str = 'add_fengniao_callback_tables'
down_revision: Union[str, None] = 'dc3c3d25c139'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 创建蜂鸟回调记录表
    op.create_table('fengniao_callbacks',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('app_id', sa.String(length=64), nullable=False, comment='应用ID'),
        sa.Column('timestamp', BIGINT(), nullable=False, comment='时间戳(毫秒)'),
        sa.Column('update_time', sa.DateTime(), nullable=False, comment='可读时间格式'),
        sa.Column('signature', sa.String(length=256), nullable=True, comment='签名'),
        sa.Column('business_data', JSON(), nullable=False, comment='业务数据(JSON格式)'),
        sa.Column('callback_type', sa.String(length=64), nullable=False, comment='回调类型'),
        sa.Column('raw_data', JSON(), nullable=True, comment='原始回调数据'),
        sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
        sa.PrimaryKeyConstraint('id'),
        comment='蜂鸟回调记录表'
    )
    
    # 为蜂鸟回调记录表创建索引
    op.create_index('ix_fengniao_callbacks_id', 'fengniao_callbacks', ['id'])
    op.create_index('ix_fengniao_callbacks_app_id', 'fengniao_callbacks', ['app_id'])
    op.create_index('ix_fengniao_callbacks_timestamp', 'fengniao_callbacks', ['timestamp'])
    op.create_index('ix_fengniao_callbacks_callback_type', 'fengniao_callbacks', ['callback_type'])
    
    # 创建蜂鸟订单状态回调详细信息表
    op.create_table('fengniao_callbacks_order_status',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('callback_id', sa.Integer(), nullable=False, comment='回调记录ID'),
        sa.Column('order_id', BIGINT(), nullable=True, comment='蜂鸟订单号'),
        sa.Column('app_id', sa.String(length=64), nullable=True, comment='应用ID'),
        sa.Column('partner_order_code', sa.String(length=128), nullable=True, comment='外部订单号'),
        sa.Column('order_status', sa.Integer(), nullable=True, comment='订单状态'),
        sa.Column('carrier_driver_id', BIGINT(), nullable=True, comment='骑手ID'),
        sa.Column('carrier_driver_name', sa.String(length=64), nullable=True, comment='骑手姓名'),
        sa.Column('carrier_driver_phone', sa.String(length=32), nullable=True, comment='骑手电话'),
        sa.Column('carrier_lat', sa.String(length=32), nullable=True, comment='骑手纬度'),
        sa.Column('carrier_lng', sa.String(length=32), nullable=True, comment='骑手经度'),
        sa.Column('description', sa.Text(), nullable=True, comment='描述'),
        sa.Column('detail_description', sa.Text(), nullable=True, comment='详情描述'),
        sa.Column('error_code', sa.String(length=64), nullable=True, comment='异常code'),
        sa.Column('error_scene', sa.String(length=128), nullable=True, comment='异常描述'),
        sa.Column('push_time', BIGINT(), nullable=True, comment='状态推送时间(毫秒)'),
        sa.Column('transfer', sa.Integer(), nullable=True, comment='转单标识(1是转单,0非转单)'),
        sa.Column('api_code', sa.String(length=64), nullable=True, comment='订单状态回调错误码'),
        sa.Column('api_msg', sa.Text(), nullable=True, comment='订单状态回调错误信息描述'),
        sa.Column('complete_pics', JSON(), nullable=True, comment='送达照片URL列表'),
        sa.Column('state', sa.Integer(), nullable=True, comment='逆向运单状态'),
        sa.Column('shipping_order_id', BIGINT(), nullable=True, comment='正向运单ID'),
        sa.Column('reverse_shipping_order_id', BIGINT(), nullable=True, comment='售中逆向运单ID'),
        sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
        sa.ForeignKeyConstraint(['callback_id'], ['fengniao_callbacks.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='蜂鸟订单状态回调详细信息表'
    )
    
    # 为蜂鸟订单状态回调详细信息表创建索引
    op.create_index('ix_fengniao_callbacks_order_status_id', 'fengniao_callbacks_order_status', ['id'])
    op.create_index('ix_fengniao_callbacks_order_status_callback_id', 'fengniao_callbacks_order_status', ['callback_id'])
    op.create_index('ix_fengniao_callbacks_order_status_order_id', 'fengniao_callbacks_order_status', ['order_id'])
    op.create_index('ix_fengniao_callbacks_order_status_partner_order_code', 'fengniao_callbacks_order_status', ['partner_order_code'])


def downgrade() -> None:
    """Downgrade schema."""
    # 删除索引
    op.drop_index('ix_fengniao_callbacks_order_status_partner_order_code', table_name='fengniao_callbacks_order_status')
    op.drop_index('ix_fengniao_callbacks_order_status_order_id', table_name='fengniao_callbacks_order_status')
    op.drop_index('ix_fengniao_callbacks_order_status_callback_id', table_name='fengniao_callbacks_order_status')
    op.drop_index('ix_fengniao_callbacks_order_status_id', table_name='fengniao_callbacks_order_status')
    
    op.drop_index('ix_fengniao_callbacks_callback_type', table_name='fengniao_callbacks')
    op.drop_index('ix_fengniao_callbacks_timestamp', table_name='fengniao_callbacks')
    op.drop_index('ix_fengniao_callbacks_app_id', table_name='fengniao_callbacks')
    op.drop_index('ix_fengniao_callbacks_id', table_name='fengniao_callbacks')
    
    # 删除表
    op.drop_table('fengniao_callbacks_order_status')
    op.drop_table('fengniao_callbacks')
