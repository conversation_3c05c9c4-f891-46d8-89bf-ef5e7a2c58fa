import logging
from datetime import datetime

from fastapi import APIRouter, Request

fengniao_router = APIRouter()
logger = logging.getLogger(__name__)


@fengniao_router.post("/fengniao", summary="外部系统回调接口")
async def external_callback(request: Request):
    body = await request.json()
    print(f"[{datetime.now()}] 外部系统回调信息: {body}")
    return {"code": 200, "status": "success", "message": "Callback processed"}
