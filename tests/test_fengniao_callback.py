"""蜂鸟回调接口测试"""
import json
import pytest
from datetime import datetime
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.dao.fengniao import fengniao_callback_dao, fengniao_callback_order_status_dao


class TestFengniaoCallback:
    """蜂鸟回调接口测试类"""
    
    def test_order_status_callback(self, client: TestClient, session: Session):
        """测试订单状态变更回调"""
        # 模拟蜂鸟订单状态回调数据
        callback_data = {
            "app_id": "7625XXX37",
            "signature": "test_signature",
            "timestamp": "1610629131295",
            "business_data": json.dumps({
                "callback_business_type": "orderStatusNotify",
                "param": {
                    "app_id": "7625XXX37",
                    "carrier_driver_id": None,
                    "carrier_driver_name": "",
                    "carrier_driver_phone": "",
                    "carrier_lat": None,
                    "carrier_lng": None,
                    "description": "因用户原因导致无法继续配送",
                    "detail_description": "用户发起取消",
                    "error_code": "USER_CANCEL",
                    "error_scene": "CUSTOMER_ERROR",
                    "order_id": 100000000220275300,
                    "order_status": 5,
                    "partner_order_code": "9405228041612191",
                    "push_time": 1610629131295
                }
            }, ensure_ascii=False)
        }
        
        # 发送回调请求
        response = client.post("/callback/fengniao", json=callback_data)
        
        # 验证响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == 200
        assert response_data["message"] == "success"
        assert response_data["data"]["callback_type"] == "orderStatusNotify"
        
        # 验证数据库中的数据
        callbacks = fengniao_callback_dao.get_by_callback_type(session, "orderStatusNotify")
        assert len(callbacks) > 0
        
        callback = callbacks[0]
        assert callback.app_id == "7625XXX37"
        assert callback.callback_type == "orderStatusNotify"
        
        # 验证订单状态详细信息
        order_status_callbacks = fengniao_callback_order_status_dao.get_by_partner_order_code(
            session, "9405228041612191"
        )
        assert len(order_status_callbacks) > 0
        
        order_callback = order_status_callbacks[0]
        assert order_callback.order_id == 100000000220275300
        assert order_callback.partner_order_code == "9405228041612191"
        assert order_callback.order_status == 5
        assert order_callback.error_code == "USER_CANCEL"
    
    def test_chainstore_service_status_callback(self, client: TestClient, session: Session):
        """测试门店配送范围变更回调"""
        callback_data = {
            "app_id": "7654231944014722107",
            "signature": "60dc8bc45a252bd30aaac92f0e1eb8c32836d189a2babe2aec8822d0845395cd",
            "timestamp": "1663258191001",
            "business_data": json.dumps({
                "callback_business_type": "chainstoreServiceStatusNotify",
                "param": {
                    "chain_store_id": "212870531",
                    "merchant_id": "3857579",
                    "option_type": 2001,
                    "out_shop_code": "271731382"
                }
            }, ensure_ascii=False)
        }
        
        # 发送回调请求
        response = client.post("/callback/fengniao", json=callback_data)
        
        # 验证响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == 200
        assert response_data["message"] == "success"
        assert response_data["data"]["callback_type"] == "chainstoreServiceStatusNotify"
        
        # 验证数据库中的数据
        callbacks = fengniao_callback_dao.get_by_callback_type(session, "chainstoreServiceStatusNotify")
        assert len(callbacks) > 0
        
        callback = callbacks[0]
        assert callback.app_id == "7654231944014722107"
        assert callback.callback_type == "chainstoreServiceStatusNotify"
    
    def test_invalid_callback_data(self, client: TestClient):
        """测试无效的回调数据"""
        # 发送无效的回调数据
        invalid_data = {
            "invalid_field": "invalid_value"
        }
        
        response = client.post("/callback/fengniao", json=invalid_data)
        
        # 应该返回成功，但会记录错误日志
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == 200
    
    def test_malformed_business_data(self, client: TestClient):
        """测试格式错误的business_data"""
        callback_data = {
            "app_id": "test_app_id",
            "timestamp": "1610629131295",
            "business_data": "invalid_json_string"
        }
        
        response = client.post("/callback/fengniao", json=callback_data)
        
        # 应该返回成功，但business_data解析会失败
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == 200
